/* global chrome */
import { useEffect, useState } from 'react';
import {
    ConfirmationModal,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    LanguageSelector,
    OptionsButtons,
    ProtectionToggle,
    SaveButton,
    SensitivitySelector,
    UICustomization,
    WhitelistSection
} from './components';
import Login from './components/Login.jsx';
import SyncSection from './components/SyncSection.jsx';

export default function App() {
  const [language, setLanguage] = useState('Mixed');
  const [sensitivity, setSensitivity] = useState('High');
  const [protectionEnabled, setProtectionEnabled] = useState(true);
  const [whitelistOptions, setWhitelistOptions] = useState(false);
  const [uiOptions, setUiOptions] = useState(false);
  
  // Whitelist state
  const [whitelistTerms, setWhitelistTerms] = useState([]);
  const [whitelistWebsites, setWhitelistWebsites] = useState([]);
  const [newTerm, setNewTerm] = useState('');
  const [newWebsite, setNewWebsite] = useState('');
  const [editingTerm, setEditingTerm] = useState(null);
  const [editingWebsite, setEditingWebsite] = useState(null);
  
  // UI Customization state
  const [flagStyle, setFlagStyle] = useState('asterisk');
  const [showHighlight, setShowHighlight] = useState(true);
  const [highlightColor, setHighlightColor] = useState('#ffeb3b');

  // Account state
  const [accountName, setAccountName] = useState('John Doe');
  const [accountStatus] = useState('Premium');
  const [userEmail, setUserEmail] = useState('');

  // Save and modal state
  const [hasModifications, setHasModifications] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Sync state
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState('Not synced');

  // Load settings on component mount
  useEffect(() => {
    // Check localStorage first
    if (localStorage.getItem('murai_logged_in') === 'true') {
      setIsLoggedIn(true);
      loadUserData();
      loadSettings();
      return;
    }
    // Fallback: check chrome.storage.local
    if (chrome && chrome.storage && chrome.storage.local) {
      chrome.storage.local.get(['murai_logged_in', 'murai_auth_data'], (result) => {
        if (result.murai_logged_in === 'true') {
          setIsLoggedIn(true);
          if (result.murai_auth_data) {
            loadUserDataFromStorage(result.murai_auth_data);
          }
        }
        loadSettings();
      });
    } else {
      loadSettings();
    }
  }, []);

  // Load user data from stored auth data
  const loadUserData = () => {
    try {
      const authDataStr = localStorage.getItem('murai_auth_data');
      if (authDataStr) {
        const authData = JSON.parse(authDataStr);
        loadUserDataFromStorage(authData);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const loadUserDataFromStorage = (authData) => {
    if (authData && authData.user) {
      setAccountName(authData.user.name || 'User');
      setUserEmail(authData.user.email || '');

      // Check if we have sync data
      const syncTime = localStorage.getItem('murai_last_sync');
      if (syncTime) {
        setLastSyncTime(new Date(syncTime));
        setSyncStatus('Synced');
      }
    }
  };

  // Load settings from chrome storage
  const loadSettings = async () => {
    try {
      const result = await chrome.storage.sync.get([
        'language',
        'sensitivity',
        'protectionEnabled',
        'whitelistTerms',
        'whitelistWebsites',
        'flagStyle',
        'showHighlight',
        'highlightColor'
      ]);

      if (result.language) setLanguage(result.language);
      if (result.sensitivity) setSensitivity(result.sensitivity);
      if (result.protectionEnabled !== undefined) setProtectionEnabled(result.protectionEnabled);
      if (result.whitelistTerms) setWhitelistTerms(result.whitelistTerms);
      if (result.whitelistWebsites) setWhitelistWebsites(result.whitelistWebsites);
      if (result.flagStyle) setFlagStyle(result.flagStyle);
      if (result.showHighlight !== undefined) setShowHighlight(result.showHighlight);
      if (result.highlightColor) setHighlightColor(result.highlightColor);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  // Save settings to chrome storage
  const saveSettings = async (settings) => {
    try {
      await chrome.storage.sync.set(settings);
      
      // Notify content scripts about settings update
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { type: 'SETTINGS_UPDATED' });
      }
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  // Track modifications
  const trackModification = () => {
    if (!hasModifications) {
      setHasModifications(true);
    }
  };

  // Whitelist functions
  const addTerm = () => {
    if (newTerm.trim()) {
      const updatedTerms = [...whitelistTerms, newTerm.trim()];
      setWhitelistTerms(updatedTerms);
      setNewTerm('');
      trackModification();
    }
  };

  const addWebsite = () => {
    if (newWebsite.trim()) {
      const updatedWebsites = [...whitelistWebsites, newWebsite.trim()];
      setWhitelistWebsites(updatedWebsites);
      setNewWebsite('');
      trackModification();
    }
  };

  const removeTerm = (index) => {
    const updatedTerms = whitelistTerms.filter((_, i) => i !== index);
    setWhitelistTerms(updatedTerms);
    trackModification();
  };

  const removeWebsite = (index) => {
    const updatedWebsites = whitelistWebsites.filter((_, i) => i !== index);
    setWhitelistWebsites(updatedWebsites);
    trackModification();
  };

  const startEditTerm = (editData) => {
    setEditingTerm(editData);
  };

  const startEditWebsite = (editData) => {
    setEditingWebsite(editData);
  };

  const saveEditTerm = () => {
    if (editingTerm && editingTerm.value.trim()) {
      const updatedTerms = [...whitelistTerms];
      updatedTerms[editingTerm.index] = editingTerm.value.trim();
      setWhitelistTerms(updatedTerms);
      setEditingTerm(null);
      trackModification();
    }
  };

  const saveEditWebsite = () => {
    if (editingWebsite && editingWebsite.value.trim()) {
      const updatedWebsites = [...whitelistWebsites];
      updatedWebsites[editingWebsite.index] = editingWebsite.value.trim();
      setWhitelistWebsites(updatedWebsites);
      setEditingWebsite(null);
      trackModification();
    }
  };

  const cancelEdit = () => {
    setEditingTerm(null);
    setEditingWebsite(null);
  };

  const handleAccountClick = () => {
    // For now, just log the click - this would typically open account settings
    console.log('Account clicked');
  };

  // Sync functionality
  const handleSyncSettings = async () => {
    setIsSyncing(true);
    setSyncStatus('Syncing...');

    try {
      // Get auth data
      const authDataStr = localStorage.getItem('murai_auth_data');
      if (!authDataStr) {
        throw new Error('Not authenticated');
      }

      const authData = JSON.parse(authDataStr);
      const API_BASE_URL = 'https://murai-server.onrender.com/api';

      // Fetch latest settings from mobile app
      const response = await fetch(`${API_BASE_URL}/users/preferences`, {
        headers: {
          'Authorization': `Bearer ${authData.token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const preferences = await response.json();

      // Convert mobile app preferences to extension format
      const extensionSettings = {
        language: preferences.language || 'Mixed',
        sensitivity: preferences.sensitivity || 'High',
        protectionEnabled: true,
        whitelistTerms: preferences.whitelistTerms || [],
        whitelistWebsites: preferences.whitelistSite || [],
        flagStyle: preferences.flagStyle || 'asterisk',
        showHighlight: preferences.isHighlighted !== false,
        highlightColor: preferences.color || '#ffeb3b'
      };

      // Update local state
      setLanguage(extensionSettings.language);
      setSensitivity(extensionSettings.sensitivity);
      setProtectionEnabled(extensionSettings.protectionEnabled);
      setWhitelistTerms(extensionSettings.whitelistTerms);
      setWhitelistWebsites(extensionSettings.whitelistWebsites);
      setFlagStyle(extensionSettings.flagStyle);
      setShowHighlight(extensionSettings.showHighlight);
      setHighlightColor(extensionSettings.highlightColor);

      // Save to chrome storage
      await chrome.storage.sync.set(extensionSettings);

      // Log sync activity to server
      await fetch(`${API_BASE_URL}/users/extension-sync`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authData.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          syncType: 'manual',
          extensionVersion: chrome.runtime.getManifest().version,
          userAgent: navigator.userAgent
        })
      });

      // Notify content scripts about settings update
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { type: 'SETTINGS_UPDATED' });
      }

      // Update sync status
      const now = new Date();
      setLastSyncTime(now);
      setSyncStatus('Synced');
      localStorage.setItem('murai_last_sync', now.toISOString());

      console.log('Settings synced successfully from mobile app');

    } catch (error) {
      console.error('Sync error:', error);
      setSyncStatus('Sync failed');
      setTimeout(() => setSyncStatus('Not synced'), 3000);
    } finally {
      setIsSyncing(false);
    }
  };

  // Save functions
  const handleSaveClick = () => {
    setShowModal(true);
  };

  const handleConfirmSave = async () => {
    const settings = {
      language,
      sensitivity,
      protectionEnabled,
      whitelistTerms,
      whitelistWebsites,
      flagStyle,
      showHighlight,
      highlightColor
    };

    await saveSettings(settings);
    
    console.log('Settings saved:', settings);
    setHasModifications(false);
    setShowModal(false);
  };

  const handleCancelSave = () => {
    setShowModal(false);
  };

  // Track changes to main settings
  const handleLanguageChange = (newLanguage) => {
    setLanguage(newLanguage);
    trackModification();
  };

  const handleSensitivityChange = (newSensitivity) => {
    setSensitivity(newSensitivity);
    trackModification();
  };

  const handleProtectionChange = (newProtection) => {
    setProtectionEnabled(newProtection);
    trackModification();
  };

  const handleFlagStyleChange = (newFlagStyle) => {
    setFlagStyle(newFlagStyle);
    trackModification();
  };

  const handleHighlightChange = (newHighlight) => {
    setShowHighlight(newHighlight);
    trackModification();
  };

  const handleHighlightColorChange = (newColor) => {
    setHighlightColor(newColor);
    trackModification();
  };

  if (!isLoggedIn) {
    return <Login />;
  }

  return (
    <div className="app-container">
      <Header
        accountName={accountName}
        accountStatus={accountStatus}
        onAccountClick={handleAccountClick}
      />

      {/* Mobile App Sync Section */}
      <SyncSection
        lastSyncTime={lastSyncTime}
        isSyncing={isSyncing}
        syncStatus={syncStatus}
        onSyncClick={handleSyncSettings}
        userEmail={userEmail}
      />

      <ProtectionToggle
        protectionEnabled={protectionEnabled}
        onProtectionChange={handleProtectionChange}
      />

      <LanguageSelector 
        language={language}
        onLanguageChange={handleLanguageChange}
      />

      <SensitivitySelector 
        sensitivity={sensitivity}
        onSensitivityChange={handleSensitivityChange}
      />

      <OptionsButtons 
        whitelistOptions={whitelistOptions}
        uiOptions={uiOptions}
        onWhitelistToggle={() => setWhitelistOptions(!whitelistOptions)}
        onUiToggle={() => setUiOptions(!uiOptions)}
      />

      {/* Whitelist Section */}
      {whitelistOptions && (
        <WhitelistSection
          whitelistTerms={whitelistTerms}
          whitelistWebsites={whitelistWebsites}
          newTerm={newTerm}
          newWebsite={newWebsite}
          editingTerm={editingTerm}
          editingWebsite={editingWebsite}
          onNewTermChange={setNewTerm}
          onNewWebsiteChange={setNewWebsite}
          onAddTerm={addTerm}
          onAddWebsite={addWebsite}
          onRemoveTerm={removeTerm}
          onRemoveWebsite={removeWebsite}
          onStartEditTerm={startEditTerm}
          onStartEditWebsite={startEditWebsite}
          onSaveEditTerm={saveEditTerm}
          onSaveEditWebsite={saveEditWebsite}
          onCancelEdit={cancelEdit}
        />
      )}

      {/* UI Customization Section */}
      {uiOptions && (
        <UICustomization
          flagStyle={flagStyle}
          showHighlight={showHighlight}
          highlightColor={highlightColor}
          onFlagStyleChange={handleFlagStyleChange}
          onHighlightChange={handleHighlightChange}
          onHighlightColorChange={handleHighlightColorChange}
        />
      )}

     

      <SaveButton 
        hasModifications={hasModifications}
        onSaveClick={handleSaveClick}
      />

      <Footer />

      <ConfirmationModal 
        showModal={showModal}
        onConfirm={handleConfirmSave}
        onCancel={handleCancelSave}
      />
    </div>
  );
} 