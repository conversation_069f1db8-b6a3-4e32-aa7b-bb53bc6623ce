{"name": "murai-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "generate-sample-data": "node scripts/generateSampleData.js", "generate-user-data": "node scripts/generateUserSpecificData.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "react-native-chart-kit": "^6.12.0", "react-native-svg": "15.11.2"}, "devDependencies": {"nodemon": "^3.1.10"}}